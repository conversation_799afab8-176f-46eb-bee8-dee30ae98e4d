@echo off
echo ========================================
echo 流萤桌宠依赖安装脚本（无需 C 编译器版本）
echo ========================================

if not exist "libs" mkdir libs

echo.
echo [1/4] 安装核心 GUI 框架...
echo ----------------------------------------
pip install --only-binary=all --target ./libs PySide6==6.7.1
if errorlevel 1 (
    echo ❌ PySide6 安装失败
    goto :error
) else (
    echo ✅ PySide6 安装成功
)

pip install --only-binary=all --target ./libs PySide6-Fluent-Widgets==1.6.0
if errorlevel 1 (
    echo ❌ PySide6-Fluent-Widgets 安装失败
) else (
    echo ✅ PySide6-Fluent-Widgets 安装成功
)

pip install --only-binary=all --target ./libs PySideSix-Frameless-Window==0.3.12
if errorlevel 1 (
    echo ❌ PySideSix-Frameless-Window 安装失败
) else (
    echo ✅ PySideSix-Frameless-Window 安装成功
)

echo.
echo [2/4] 安装 PySide6 相关组件...
echo ----------------------------------------
pip install --only-binary=all --target ./libs PySide6_Addons==6.7.1 PySide6_Essentials==6.7.1 shiboken6==6.7.1

echo.
echo [3/4] 安装其他核心依赖...
echo ----------------------------------------
pip install --only-binary=all --target ./libs loguru==0.7.2
echo ✅ loguru（日志系统）

pip install --only-binary=all --target ./libs colorama==0.4.6
echo ✅ colorama（彩色输出）

pip install --only-binary=all --target ./libs darkdetect==0.8.0
echo ✅ darkdetect（暗色主题检测）

pip install --only-binary=all --target ./libs websockets==11.0.3
echo ✅ websockets（网络通信）

pip install --only-binary=all --target ./libs altgraph==0.17.4 colorthief==0.2.1 ordered-set==4.1.0 packaging==24.1
pip install --only-binary=all --target ./libs pefile==2023.2.7 pyinstaller-hooks-contrib==2024.7
pip install --only-binary=all --target ./libs pywin32==306 pywin32-ctypes==0.2.2 setuptools==70.2.0 win32-setctime==1.1.0

echo.
echo [4/4] 尝试安装可选依赖（可能需要编译器）...
echo ----------------------------------------
echo 尝试安装 pillow（图片处理）...
pip install --only-binary=all --target ./libs pillow 2>nul
if errorlevel 1 (
    echo ⚠️  pillow 安装失败，图片功能可能受限
) else (
    echo ✅ pillow 安装成功
)

echo 尝试安装 psutil（系统监控）...
pip install --only-binary=all --target ./libs psutil 2>nul
if errorlevel 1 (
    echo ⚠️  psutil 安装失败，电池监控功能不可用
) else (
    echo ✅ psutil 安装成功
)

echo.
echo ========================================
echo 跳过的依赖（需要 C 编译器）：
echo ❌ numpy==2.0.1          （数值计算）
echo ❌ scipy==1.14.0         （科学计算）
echo ❌ PyAudio==0.2.14       （音频处理）
echo ❌ zstandard==0.22.0     （压缩算法）
echo.
echo 影响的功能：
echo - 可能无法播放音频
echo - 数值计算功能受限
echo - 高级压缩功能不可用
echo ========================================

echo.
echo ✅ 依赖安装完成！
echo 📁 依赖位置: ./libs/
echo 🚀 现在可以运行: python MyFlowingFireflyWife.py
echo 📦 或者运行: build-pyinstaller.bat 进行打包
echo.
pause
goto :end

:error
echo.
echo ❌ 安装过程中出现错误！
echo 请检查网络连接和 Python 环境。
pause

:end
