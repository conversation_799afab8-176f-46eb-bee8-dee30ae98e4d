#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动脚本：使用本地 libs 文件夹中的依赖运行流萤桌宠
"""

import sys
import os

# 添加 libs 目录到 Python 路径
libs_path = os.path.join(os.path.dirname(__file__), 'libs')
if os.path.exists(libs_path):
    sys.path.insert(0, libs_path)

    # 特别处理 pywin32 的路径设置
    win32_path = os.path.join(libs_path, 'win32')
    if os.path.exists(win32_path):
        sys.path.insert(0, win32_path)

    # 添加 pywin32_system32 到 PATH 环境变量
    pywin32_system32 = os.path.join(libs_path, 'pywin32_system32')
    if os.path.exists(pywin32_system32):
        os.environ['PATH'] = pywin32_system32 + os.pathsep + os.environ.get('PATH', '')

    # 设置 pywin32 的 .pth 文件路径
    pywin32_pth = os.path.join(libs_path, 'pywin32.pth')
    if os.path.exists(pywin32_pth):
        with open(pywin32_pth, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    pth_path = os.path.join(libs_path, line)
                    if os.path.exists(pth_path) and pth_path not in sys.path:
                        sys.path.insert(0, pth_path)

# 运行主程序
if __name__ == '__main__':
    try:
        # 导入并运行主程序
        from src.window.firefly import FireflyWindow
        from PySide6.QtWidgets import QApplication

        # 添加日志
        FireflyWindow.logger.add("log/latest.log", rotation="500 MB")

        # 创建应用
        app = QApplication(sys.argv)
        window = FireflyWindow.MainWindow(app)
        window.show()

        # 运行应用
        sys.exit(app.exec())

    except Exception as e:
        print(f"❌ 运行错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
