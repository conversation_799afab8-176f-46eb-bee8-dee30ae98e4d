#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动脚本：使用本地 libs 文件夹中的依赖运行流萤桌宠
"""

import sys
import os

# 添加 libs 目录到 Python 路径的最前面
libs_path = os.path.join(os.path.dirname(__file__), 'libs')
if os.path.exists(libs_path):
    sys.path.insert(0, libs_path)
    
    # 特别处理 pywin32 的路径
    win32_path = os.path.join(libs_path, 'win32')
    if os.path.exists(win32_path):
        sys.path.insert(0, win32_path)
    
    # 添加 pywin32_system32 到 PATH
    pywin32_system32 = os.path.join(libs_path, 'pywin32_system32')
    if os.path.exists(pywin32_system32):
        os.environ['PATH'] = pywin32_system32 + os.pathsep + os.environ.get('PATH', '')

# 现在导入并运行主程序
if __name__ == '__main__':
    try:
        # 测试关键模块导入
        print("正在测试依赖包...")
        import numpy
        print(f"✅ numpy {numpy.__version__}")
        
        import scipy
        print(f"✅ scipy {scipy.__version__}")
        
        import PIL
        print(f"✅ PIL {PIL.__version__}")
        
        import psutil
        print(f"✅ psutil {psutil.__version__}")
        
        import PySide6
        print(f"✅ PySide6 {PySide6.__version__}")
        
        import win32api
        print("✅ win32api")
        
        print("\n所有依赖包测试通过！正在启动流萤桌宠...")
        print("=" * 50)
        
        # 导入并运行主程序
        from src.window.firefly import FireflyWindow
        from PySide6.QtWidgets import QApplication
        
        # 添加日志
        FireflyWindow.logger.add("log/latest.log", rotation="500 MB")
        
        # 创建应用
        app = QApplication(sys.argv)
        window = FireflyWindow.MainWindow(app)
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请检查依赖包是否正确安装到 libs 文件夹")
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
