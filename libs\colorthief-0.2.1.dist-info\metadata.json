{"license": "BSD", "name": "colorthief", "metadata_version": "2.0", "generator": "bdist_wheel (0.24.0)", "summary": "A module for grabbing the color palette from an image.", "run_requires": [{"requires": ["Pillow"]}], "version": "0.2.1", "extensions": {"python.details": {"project_urls": {"Home": "https://github.com/fengsp/color-thief-py"}, "document_names": {"description": "DESCRIPTION.rst"}, "contacts": [{"role": "author", "email": "<EMAIL>", "name": "<PERSON><PERSON>"}]}}, "classifiers": ["Development Status :: 4 - Beta", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: BSD License", "Programming Language :: Python", "Programming Language :: Python :: 2.6", "Programming Language :: Python :: 2.7"], "extras": []}