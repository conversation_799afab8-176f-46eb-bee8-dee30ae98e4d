@echo off
echo 开始打包流萤桌宠...

REM 检查依赖是否已安装
if not exist "libs" (
    echo 错误：libs 文件夹不存在！
    echo 请先运行依赖安装脚本或手动安装依赖到 libs 文件夹
    pause
    exit /b 1
)

REM 安装 pyinstaller（打包工具）
python -c "import pyinstaller" 2>nul
if errorlevel 1 (
    echo 正在安装 PyInstaller...
    pip install pyinstaller
)

REM 清理之前的打包文件
echo 清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

REM 开始打包
echo 正在打包，请稍候...

pyinstaller --onefile ^
    --windowed ^
    --add-data "data;data" ^
    --add-data "src;src" ^
    --add-data "libs;libs" ^
    --paths "./libs" ^
    --icon "data/assets/images/firefly/icon/icon.png" ^
    --name "MyFlowingFireflyWife" ^
    --hidden-import "PySide6.QtCore" ^
    --hidden-import "PySide6.QtWidgets" ^
    --hidden-import "PySide6.QtGui" ^
    --hidden-import "PySide6.QtWebEngineWidgets" ^
    --hidden-import "qfluentwidgets" ^
    --hidden-import "loguru" ^
    --hidden-import "psutil" ^
    --hidden-import "PIL" ^
    --hidden-import "win32api" ^
    --collect-all "PySide6" ^
    --collect-all "qfluentwidgets" ^
    run_with_libs.py

if errorlevel 1 (
    echo 打包失败！请检查错误信息。
    pause
    exit /b 1
)

echo 打包完成！
echo 可执行文件位置: dist\MyFlowingFireflyWife.exe
pause
    --hidden-import "psutil" ^
    --hidden-import "PIL" ^
    --collect-all "PySide6" ^
    --collect-all "qfluentwidgets" ^
    MyFlowingFireflyWife.py

if errorlevel 1 (
    echo 打包失败！请检查错误信息。
    pause
    exit /b 1
)

echo 打包完成！
echo 依赖文件夹: libs\
echo 可执行文件位置: dist\MyFlowingFireflyWife.exe
echo 注意：运行 exe 文件时不需要 libs 文件夹，所有依赖已打包进 exe
pause