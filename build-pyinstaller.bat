@echo off
echo 开始打包流萤桌宠...

REM 第一步：下载依赖到 ./libs 文件夹
echo 正在下载依赖到 ./libs 文件夹...
if not exist "libs" mkdir libs

REM 先尝试安装所有依赖到 libs
echo 尝试安装所有依赖...
pip install --only-binary=all --target ./libs -r requirement.txt
if errorlevel 1 (
    echo 部分依赖安装失败，尝试安装核心依赖...
    pip install --target ./libs PySide6==6.7.1 PySide6-Fluent-Widgets==1.6.0 PySideSix-Frameless-Window==0.3.12
    pip install --target ./libs loguru==0.7.2 colorama==0.4.6 darkdetect==0.8.0 websockets==11.0.3
    pip install --target ./libs pillow psutil pywin32
)

REM 安装 pyinstaller 到系统（打包工具本身不能放在 libs 里）
python -c "import pyinstaller" 2>nul
if errorlevel 1 (
    echo 正在安装 PyInstaller...
    pip install pyinstaller
)

REM 第二步：清理之前的打包文件
echo 清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

REM 第三步：设置 Python 路径并开始打包
echo 正在打包，请稍候...
set PYTHONPATH=./libs;%PYTHONPATH%

pyinstaller --onefile ^
    --windowed ^
    --add-data "data;data" ^
    --add-data "src;src" ^
    --add-data "libs;libs" ^
    --paths "./libs" ^
    --icon "data/assets/images/firefly/icon/icon.png" ^
    --name "MyFlowingFireflyWife" ^
    --hidden-import "PySide6.QtCore" ^
    --hidden-import "PySide6.QtWidgets" ^
    --hidden-import "PySide6.QtGui" ^
    --hidden-import "PySide6.QtWebEngineWidgets" ^
    --hidden-import "qfluentwidgets" ^
    --hidden-import "loguru" ^
    --hidden-import "psutil" ^
    --hidden-import "PIL" ^
    --collect-all "PySide6" ^
    --collect-all "qfluentwidgets" ^
    MyFlowingFireflyWife.py

if errorlevel 1 (
    echo 打包失败！请检查错误信息。
    pause
    exit /b 1
)

echo 打包完成！
echo 依赖文件夹: libs\
echo 可执行文件位置: dist\MyFlowingFireflyWife.exe
echo 注意：运行 exe 文件时不需要 libs 文件夹，所有依赖已打包进 exe
pause