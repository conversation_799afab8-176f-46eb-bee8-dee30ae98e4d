@echo off
echo 开始打包流萤桌宠...

REM 检查是否安装了 pyinstaller
python -c "import pyinstaller" 2>nul
if errorlevel 1 (
    echo 正在安装 PyInstaller...
    pip install pyinstaller
)

REM 清理之前的打包文件
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

REM 开始打包
echo 正在打包，请稍候...
pyinstaller --onefile ^
    --windowed ^
    --add-data "data;data" ^
    --add-data "src;src" ^
    --icon "data/assets/images/firefly/icon/icon.png" ^
    --name "MyFlowingFireflyWife" ^
    --hidden-import "PySide6.QtCore" ^
    --hidden-import "PySide6.QtWidgets" ^
    --hidden-import "PySide6.QtGui" ^
    --hidden-import "PySide6.QtWebEngineWidgets" ^
    --hidden-import "qfluentwidgets" ^
    --hidden-import "loguru" ^
    --hidden-import "psutil" ^
    --hidden-import "PIL" ^
    --collect-all "PySide6" ^
    --collect-all "qfluentwidgets" ^
    MyFlowingFireflyWife.py

if errorlevel 1 (
    echo 打包失败！请检查错误信息。
    pause
    exit /b 1
)

echo 打包完成！
echo 可执行文件位置: dist\MyFlowingFireflyWife.exe
pause