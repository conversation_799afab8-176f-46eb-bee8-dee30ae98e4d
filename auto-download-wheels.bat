@echo off
echo ========================================
echo 自动下载预编译 wheel 文件脚本
echo ========================================

REM 创建必要的文件夹
if not exist "libs" mkdir libs
if not exist "wheels" mkdir wheels

REM 获取 Python 版本
for /f "tokens=1,2 delims=." %%a in ('python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')"') do (
    set PYTHON_MAJOR=%%a
    set PYTHON_MINOR=%%b
)

echo Python 版本: %PYTHON_MAJOR%.%PYTHON_MINOR%
echo.

REM 尝试从 PyPI 下载预编译包
echo 尝试从 PyPI 下载预编译包...
echo ========================================

echo [1/6] 下载 numpy...
pip download --only-binary=:all: --dest wheels numpy==2.0.1
if errorlevel 1 echo ⚠️  numpy 下载失败

echo [2/6] 下载 scipy...
pip download --only-binary=:all: --dest wheels scipy==1.14.0
if errorlevel 1 echo ⚠️  scipy 下载失败

echo [3/6] 下载 PyAudio...
pip download --only-binary=:all: --dest wheels PyAudio==0.2.14
if errorlevel 1 echo ⚠️  PyAudio 下载失败

echo [4/6] 下载 psutil...
pip download --only-binary=:all: --dest wheels psutil==6.0.0
if errorlevel 1 echo ⚠️  psutil 下载失败

echo [5/6] 下载 Pillow...
pip download --only-binary=:all: --dest wheels Pillow==10.4.0
if errorlevel 1 echo ⚠️  Pillow 下载失败

echo [6/6] 下载 zstandard...
pip download --only-binary=:all: --dest wheels zstandard==0.22.0
if errorlevel 1 echo ⚠️  zstandard 下载失败

echo.
echo ========================================
echo 安装下载的 wheel 文件到 ./libs...
echo ========================================

REM 安装所有下载的 wheel 文件
for %%f in (wheels\*.whl) do (
    echo 安装: %%~nxf
    pip install --target ./libs "%%f" --no-deps
    if errorlevel 1 (
        echo ❌ %%~nxf 安装失败
    ) else (
        echo ✅ %%~nxf 安装成功
    )
)

echo.
echo ========================================
echo 安装其他依赖...
echo ========================================
pip install --only-binary=all --target ./libs PySide6==6.7.1 PySide6-Fluent-Widgets==1.6.0 PySideSix-Frameless-Window==0.3.12
pip install --only-binary=all --target ./libs PySide6_Addons==6.7.1 PySide6_Essentials==6.7.1 shiboken6==6.7.1
pip install --only-binary=all --target ./libs loguru==0.7.2 colorama==0.4.6 darkdetect==0.8.0 websockets==11.0.3
pip install --only-binary=all --target ./libs altgraph==0.17.4 colorthief==0.2.1 ordered-set==4.1.0 packaging==24.1
pip install --only-binary=all --target ./libs pefile==2023.2.7 pyinstaller-hooks-contrib==2024.7
pip install --only-binary=all --target ./libs pywin32==306 pywin32-ctypes==0.2.2 setuptools==70.2.0 win32-setctime==1.1.0

echo.
echo ========================================
echo 完成！
echo ========================================
echo ✅ 依赖安装到: ./libs/
echo 📦 wheel 文件保存在: ./wheels/
echo.
echo 测试安装是否成功：
set PYTHONPATH=./libs;%PYTHONPATH%
python -c "import numpy, scipy, PIL, psutil; print('✅ 所有关键包导入成功！')" 2>nul
if errorlevel 1 (
    echo ⚠️  部分包可能安装失败，但基础功能应该可用
) else (
    echo ✅ 所有关键包测试通过！
)

echo.
echo 现在可以运行：
echo   set PYTHONPATH=./libs;%%PYTHONPATH%% ^&^& python MyFlowingFireflyWife.py
echo 或者打包：
echo   build-pyinstaller.bat
pause
