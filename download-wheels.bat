@echo off
echo ========================================
echo 手动下载预编译 wheel 文件脚本
echo ========================================

REM 检查 Python 版本
echo 检查 Python 版本...
python -c "import sys, platform; print(f'Python {sys.version_info.major}.{sys.version_info.minor}, {platform.architecture()[0]}, {platform.system()}')"

REM 创建必要的文件夹
if not exist "libs" mkdir libs
if not exist "wheels" mkdir wheels

echo.
echo ========================================
echo 请按照以下步骤手动下载 wheel 文件：
echo ========================================
echo.
echo 1. 打开浏览器，访问：https://www.lfd.uci.edu/~gohlke/pythonlibs/
echo.
echo 2. 根据你的 Python 版本下载以下文件到 wheels 文件夹：
echo.

REM 获取 Python 版本信息
for /f "tokens=1,2 delims=." %%a in ('python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')"') do (
    set PYTHON_VERSION=%%a%%b
)

echo    对于 Python 3.%PYTHON_VERSION:~1% (64位)，下载：
echo    ----------------------------------------
echo    📦 numpy‑2.0.1‑cp3%PYTHON_VERSION:~1%‑cp3%PYTHON_VERSION:~1%‑win_amd64.whl
echo    📦 scipy‑1.14.0‑cp3%PYTHON_VERSION:~1%‑cp3%PYTHON_VERSION:~1%‑win_amd64.whl
echo    📦 PyAudio‑0.2.14‑cp3%PYTHON_VERSION:~1%‑cp3%PYTHON_VERSION:~1%‑win_amd64.whl
echo    📦 psutil‑6.0.0‑cp3%PYTHON_VERSION:~1%‑cp3%PYTHON_VERSION:~1%‑win_amd64.whl
echo    📦 Pillow‑10.4.0‑cp3%PYTHON_VERSION:~1%‑cp3%PYTHON_VERSION:~1%‑win_amd64.whl
echo    📦 zstandard‑0.22.0‑cp3%PYTHON_VERSION:~1%‑cp3%PYTHON_VERSION:~1%‑win_amd64.whl
echo.
echo    注意：如果找不到确切版本，选择最接近的版本
echo    注意：如果是 32位 Python，选择 win32 而不是 win_amd64
echo.
echo 3. 下载完成后，将所有 .whl 文件放到 wheels 文件夹中
echo.
echo 4. 按任意键继续安装...
pause

echo.
echo ========================================
echo 开始安装下载的 wheel 文件...
echo ========================================

REM 检查 wheels 文件夹是否有文件
dir /b wheels\*.whl >nul 2>&1
if errorlevel 1 (
    echo ❌ wheels 文件夹中没有找到 .whl 文件
    echo 请先下载 wheel 文件到 wheels 文件夹
    pause
    exit /b 1
)

REM 安装所有 wheel 文件到 libs
echo 安装 wheel 文件到 ./libs ...
for %%f in (wheels\*.whl) do (
    echo 正在安装: %%f
    pip install --target ./libs "%%f"
    if errorlevel 1 (
        echo ⚠️  %%f 安装失败
    ) else (
        echo ✅ %%f 安装成功
    )
)

echo.
echo ========================================
echo 安装其他不需要编译的依赖...
echo ========================================
pip install --only-binary=all --target ./libs PySide6==6.7.1 PySide6-Fluent-Widgets==1.6.0 PySideSix-Frameless-Window==0.3.12
pip install --only-binary=all --target ./libs PySide6_Addons==6.7.1 PySide6_Essentials==6.7.1 shiboken6==6.7.1
pip install --only-binary=all --target ./libs loguru==0.7.2 colorama==0.4.6 darkdetect==0.8.0 websockets==11.0.3
pip install --only-binary=all --target ./libs altgraph==0.17.4 colorthief==0.2.1 ordered-set==4.1.0 packaging==24.1
pip install --only-binary=all --target ./libs pefile==2023.2.7 pyinstaller-hooks-contrib==2024.7
pip install --only-binary=all --target ./libs pywin32==306 pywin32-ctypes==0.2.2 setuptools==70.2.0 win32-setctime==1.1.0

echo.
echo ✅ 所有依赖安装完成！
echo 📁 依赖位置: ./libs/
echo 🗂️  wheel 文件: ./wheels/
echo.
echo 现在可以运行：
echo   python MyFlowingFireflyWife.py
echo 或者打包：
echo   build-pyinstaller.bat
echo.
pause
