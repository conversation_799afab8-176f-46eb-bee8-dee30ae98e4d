[General]
# We base this configuration on the default config.
# You can list "Based On" as many times as you like
Based On          = default

[Keys]
# Only list keys different to default.
# Note you may wish to rebind some of the default
# Pythonwin keys to "Beep" or "DoNothing"

Alt+L             = LocateSelectedFile
Ctrl+Q            = AppExit

# Other non-default Pythonwin keys
Alt+A             = EditSelectAll
Alt+M             = LocateModule

# Movement
Ctrl+D            = GotoEndOfFile

# Tabs and other indent features
Alt+T            = <<toggle-tabs>>
Ctrl+[            = <<indent-region>>
Ctrl+]            = <<dedent-region>>

[Keys:Interactive]
Alt+P             = <<history-previous>>
Alt+N             = <<history-next>>
