"""
【课程内容】：06-桌面宠物

【授课老师】：安然

【环境使用】：
    Python3.9
    PyCharm

【模块使用】：
    PySide6 ---> pip install PySide6
"""


# -*- coding: utf-8 -*-
import sys
from PySide6.QtWidgets import QApplication
from src.window.firefly import FireflyWindow

if __name__ == '__main__':
    FireflyWindow.logger.add("log/latest.log", rotation="500 MB")
    app = QApplication(sys.argv)
    window = FireflyWindow.MainWindow(app)
    window.show()
    app.exec()
    sys.exit(0)
